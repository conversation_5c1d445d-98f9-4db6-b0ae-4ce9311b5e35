<template>
  <div class="inline-block" :class="wrapperClass">
    <!-- 点击元素复制模式 -->
    <div
      v-if="type === 'click'"
      class="cursor-pointer transition-opacity duration-200 hover:opacity-80"
      :class="clickAreaClass"
      @click="handleCopy"
    >
      <slot />
    </div>

    <!-- 图标复制模式 -->
    <div v-else class="relative inline-block group" :class="containerClass">
      <slot />

      <!-- 复制图标 -->
      <div
        class="absolute cursor-pointer p-0.5 rounded-sm transition-all duration-200 bg-white/90 dark:bg-gray-800/90 border border-gray-300 dark:border-gray-600 flex items-center justify-center z-10 opacity-0 invisible group-hover:opacity-100 group-hover:visible hover:bg-gray-100 dark:hover:bg-gray-700 hover:border-blue-400 dark:hover:border-blue-500"
        :class="[iconClass, positionClass]"
        @click="handleCopy"
        :title="copyTitle"
      >
        <CopyOutlined :style="iconStyle" class="hover:text-blue-500 dark:hover:text-blue-400 text-gray-600 dark:text-gray-300" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, type CSSProperties } from 'vue';
import { CopyOutlined } from '@ant-design/icons-vue';
import { useClipboard } from '@vueuse/core';
import { message } from 'ant-design-vue';
import { $t } from '@vben/locales';

export interface CopyWrapperProps {
  /** 复制的内容 */
  copyText: string;
  /** 复制方式：click-点击元素复制，icon-点击图标复制 */
  type?: 'click' | 'icon';
  /** 图标位置：仅在type为icon时有效 */
  position?: 'top' | 'right' | 'bottom' | 'left';
  /** 复制成功提示文本 */
  successMessage?: string;
  /** 图标大小 */
  iconSize?: number;
  /** 图标颜色 */
  iconColor?: string;
  /** 自定义样式类名 */
  wrapperClass?: string;
  /** 点击区域样式类名 */
  clickAreaClass?: string;
  /** 容器样式类名 */
  containerClass?: string;
  /** 图标样式类名 */
  iconClass?: string;
  /** 复制图标提示文本 */
  copyTitle?: string;
}

const props = withDefaults(defineProps<CopyWrapperProps>(), {
  type: 'click',
  position: 'right',
  successMessage: '',
  iconSize: 14,
  iconColor: '#666',
  wrapperClass: '',
  clickAreaClass: '',
  containerClass: '',
  iconClass: '',
  copyTitle: '复制',
});

const { copy, isSupported } = useClipboard();

// 计算图标样式
const iconStyle = computed<CSSProperties>(() => ({
  fontSize: `${props.iconSize}px`,
  color: props.iconColor,
}));

// 计算图标位置样式类
const positionClass = computed(() => {
  const positionMap = {
    top: 'bottom-full left-1/2 -translate-x-1/2 mb-1',
    right: 'top-1/2 left-full -translate-y-1/2 ml-1',
    bottom: 'top-full left-1/2 -translate-x-1/2 mt-1',
    left: 'top-1/2 right-full -translate-y-1/2 mr-1',
  };
  return positionMap[props.position];
});

// 处理复制操作
const handleCopy = async () => {
  if (!isSupported.value) {
    message.error('当前浏览器不支持复制功能');
    return;
  }

  if (!props.copyText) {
    message.warning('没有可复制的内容');
    return;
  }

  try {
    await copy(props.copyText);
    const successMsg = props.successMessage || $t('common.successful');
    message.success(successMsg);
  } catch (error) {
    console.error('复制失败:', error);
    message.error('复制失败');
  }
};
</script>


