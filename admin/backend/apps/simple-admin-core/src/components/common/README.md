# CopyWrapper 复制组件

一个通用的复制组件，支持两种复制方式：点击元素复制和点击图标复制。

## 功能特性

- 🎯 **两种复制模式**：点击元素复制 / 点击图标复制
- 📍 **灵活的图标位置**：支持上下左右四个方向
- 🎨 **高度可定制**：支持自定义样式、颜色、大小
- 📱 **响应式设计**：悬停显示复制图标
- 🔔 **友好的用户反馈**：复制成功提示
- 🛡️ **兼容性检测**：自动检测浏览器复制支持
- 🌙 **暗黑模式适配**：完美适配系统暗黑模式

## 基本用法

### 1. 点击元素复制

```vue
<template>
  <CopyWrapper
    type="click"
    copy-text="要复制的内容"
    success-message="复制成功！"
  >
    <Button type="primary">点击复制</Button>
  </CopyWrapper>
</template>
```

### 2. 图标复制模式

```vue
<template>
  <CopyWrapper
    type="icon"
    position="right"
    copy-text="要复制的内容"
    :icon-size="16"
    icon-color="#1890ff"
  >
    <Button>悬停显示复制图标</Button>
  </CopyWrapper>
</template>
```

## API 参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `copyText` | `string` | - | **必填**，要复制的内容 |
| `type` | `'click' \| 'icon'` | `'click'` | 复制方式 |
| `position` | `'top' \| 'right' \| 'bottom' \| 'left'` | `'right'` | 图标位置（仅在 type='icon' 时有效） |
| `successMessage` | `string` | `''` | 复制成功提示文本 |
| `iconSize` | `number` | `14` | 图标大小（px） |
| `iconColor` | `string` | `'#666'` | 图标颜色 |
| `wrapperClass` | `string` | `''` | 包装器自定义样式类 |
| `clickAreaClass` | `string` | `''` | 点击区域自定义样式类 |
| `containerClass` | `string` | `''` | 容器自定义样式类 |
| `iconClass` | `string` | `''` | 图标自定义样式类 |
| `copyTitle` | `string` | `'复制'` | 复制图标提示文本 |

## 使用场景示例

### 1. 设备编号复制

```vue
<CopyWrapper
  type="icon"
  position="right"
  copy-text="DEV-2024-001"
  success-message="设备编号已复制"
>
  <Button type="link" style="color: green;">DEV-2024-001</Button>
</CopyWrapper>
```

### 2. API Token 复制

```vue
<CopyWrapper
  type="click"
  copy-text="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  success-message="Token已复制到剪贴板"
>
  <Button type="primary" size="small">复制Token</Button>
</CopyWrapper>
```

### 3. 链接分享

```vue
<CopyWrapper
  type="icon"
  position="right"
  copy-text="https://example.com/share/123456"
  success-message="链接已复制"
  :icon-size="16"
  icon-color="#1890ff"
>
  <a href="https://example.com/share/123456" target="_blank">
    https://example.com/share/123456
  </a>
</CopyWrapper>
```

### 4. 表格中的应用

```vue
<!-- 在 schemas.ts 中使用 -->
{
  title: '设备编号',
  field: 'deviceNo',
  cellRender: ({ row }) => {
    return h(CopyWrapper, {
      type: 'icon',
      position: 'right',
      copyText: row.deviceNo,
      successMessage: '设备编号已复制'
    }, {
      default: () => h(Button, {
        type: 'link',
        style: { color: 'green' }
      }, () => row.deviceNo)
    });
  }
}
```

## 样式定制

### 自定义点击区域样式

```vue
<CopyWrapper
  type="click"
  copy-text="自定义样式内容"
  click-area-class="my-custom-click-area"
>
  <div>自定义内容</div>
</CopyWrapper>

<style>
.my-custom-click-area {
  padding: 10px;
  background: linear-gradient(45deg, #1890ff, #52c41a);
  border-radius: 6px;
  color: white;
}
</style>
```

### 自定义图标样式

```vue
<CopyWrapper
  type="icon"
  copy-text="内容"
  icon-class="my-custom-icon"
  :icon-size="20"
  icon-color="#ff4d4f"
>
  <span>内容</span>
</CopyWrapper>

<style>
.my-custom-icon {
  background: #fff2f0;
  border: 2px solid #ff4d4f;
  border-radius: 50%;
}
</style>
```

## 暗黑模式适配

组件完全适配系统的暗黑模式，会根据当前主题自动调整样式：

### 🌙 **暗黑模式特性**
- **自动适配**：无需额外配置，自动跟随系统主题
- **完美对比度**：在暗黑模式下提供合适的颜色对比度
- **一致体验**：与系统其他组件保持一致的视觉风格

### 🎨 **暗黑模式样式对比**

| 元素 | 浅色模式 | 暗黑模式 |
|------|----------|----------|
| 图标背景 | `bg-white/90` | `bg-gray-800/90` |
| 图标边框 | `border-gray-300` | `border-gray-600` |
| 悬停背景 | `hover:bg-gray-100` | `hover:bg-gray-700` |
| 悬停边框 | `hover:border-blue-400` | `hover:border-blue-500` |
| 图标颜色 | `text-gray-600` | `text-gray-300` |
| 悬停图标 | `hover:text-blue-500` | `hover:text-blue-400` |

### 💡 **使用建议**
- 组件会自动适配，无需手动处理暗黑模式
- 自定义样式时建议使用 TailwindCSS 的 `dark:` 前缀
- 图标颜色会根据主题自动调整，建议使用默认配置

## 注意事项

1. **浏览器兼容性**：组件会自动检测浏览器是否支持复制功能
2. **内容为空**：如果 `copyText` 为空，会显示警告提示
3. **响应式设计**：图标模式下，图标默认隐藏，悬停时显示
4. **事件冒泡**：图标模式不会触发子组件的点击事件
5. **暗黑模式**：组件自动适配系统暗黑模式，无需额外配置

## 导入方式

```typescript
// 单独导入
import { CopyWrapper } from '#/components/common';

// 或者从 form 组件中导入（已重新导出）
import { CopyWrapper } from '#/components/form';
```
