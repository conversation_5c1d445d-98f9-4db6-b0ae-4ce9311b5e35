<template>
  <Page>
    <div class="p-6">
      <h1 class="text-2xl font-bold mb-6">CopyWrapper 组件测试页面</h1>
      
      <!-- 基础测试 -->
      <Card title="基础功能测试" class="mb-6">
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <span class="w-24">点击复制:</span>
            <CopyWrapper
              type="click"
              copy-text="这是点击复制的测试内容"
              success-message="点击复制成功！"
            >
              <Button type="primary">点击我复制</Button>
            </CopyWrapper>
          </div>
          
          <div class="flex items-center gap-4">
            <span class="w-24">图标复制:</span>
            <CopyWrapper
              type="icon"
              position="right"
              copy-text="这是图标复制的测试内容"
              success-message="图标复制成功！"
            >
              <Button>悬停显示复制图标</Button>
            </CopyWrapper>
          </div>
        </div>
      </Card>

      <!-- 位置测试 -->
      <Card title="图标位置测试" class="mb-6">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center gap-4">
            <span class="w-16">上方:</span>
            <CopyWrapper
              type="icon"
              position="top"
              copy-text="上方图标复制内容"
              :icon-size="16"
              icon-color="#1890ff"
            >
              <Button>上方图标</Button>
            </CopyWrapper>
          </div>
          
          <div class="flex items-center gap-4">
            <span class="w-16">右侧:</span>
            <CopyWrapper
              type="icon"
              position="right"
              copy-text="右侧图标复制内容"
              :icon-size="16"
              icon-color="#52c41a"
            >
              <Button>右侧图标</Button>
            </CopyWrapper>
          </div>
          
          <div class="flex items-center gap-4">
            <span class="w-16">下方:</span>
            <CopyWrapper
              type="icon"
              position="bottom"
              copy-text="下方图标复制内容"
              :icon-size="16"
              icon-color="#fa8c16"
            >
              <Button>下方图标</Button>
            </CopyWrapper>
          </div>
          
          <div class="flex items-center gap-4">
            <span class="w-16">左侧:</span>
            <CopyWrapper
              type="icon"
              position="left"
              copy-text="左侧图标复制内容"
              :icon-size="16"
              icon-color="#eb2f96"
            >
              <Button>左侧图标</Button>
            </CopyWrapper>
          </div>
        </div>
      </Card>

      <!-- 实际应用场景 -->
      <Card title="实际应用场景" class="mb-6">
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <span class="w-24">设备编号:</span>
            <CopyWrapper
              type="icon"
              position="right"
              copy-text="DEV-2024-001"
              success-message="设备编号已复制"
            >
              <Tag color="green">DEV-2024-001</Tag>
            </CopyWrapper>
          </div>
          
          <div class="flex items-center gap-4">
            <span class="w-24">API Token:</span>
            <CopyWrapper
              type="click"
              copy-text="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c"
              success-message="Token已复制到剪贴板"
            >
              <Button type="primary" size="small">复制Token</Button>
            </CopyWrapper>
          </div>
          
          <div class="flex items-center gap-4">
            <span class="w-24">分享链接:</span>
            <CopyWrapper
              type="icon"
              position="right"
              copy-text="https://example.com/share/123456"
              success-message="链接已复制"
              :icon-size="14"
              icon-color="#1890ff"
            >
              <a href="https://example.com/share/123456" target="_blank" class="text-blue-500 underline">
                https://example.com/share/123456
              </a>
            </CopyWrapper>
          </div>
        </div>
      </Card>

      <!-- 自定义样式测试 -->
      <Card title="自定义样式测试">
        <div class="space-y-4">
          <div class="flex items-center gap-4">
            <span class="w-24">自定义样式:</span>
            <CopyWrapper
              type="click"
              copy-text="自定义样式的复制内容"
              success-message="自定义样式复制成功！"
              click-area-class="px-3 py-2 bg-gradient-to-r from-blue-500 to-green-500 rounded text-white transition-all duration-200 hover:-translate-y-0.5 hover:shadow-lg"
            >
              <div class="px-4 py-2 bg-gray-100 dark:bg-gray-800 rounded cursor-pointer transition-colors border-2 border-dashed border-gray-300 dark:border-gray-600 hover:bg-blue-50 dark:hover:bg-gray-700 hover:border-blue-500 dark:hover:border-blue-400">
                <span class="text-gray-800 dark:text-gray-200">自定义样式的可复制区域</span>
              </div>
            </CopyWrapper>
          </div>

          <div class="flex items-center gap-4">
            <span class="w-24">大图标:</span>
            <CopyWrapper
              type="icon"
              position="right"
              copy-text="大图标复制内容"
              :icon-size="20"
              icon-color="#ff4d4f"
              icon-class="!bg-red-50 dark:!bg-red-900/20 !border-2 !border-red-400 dark:!border-red-500 !rounded-full"
            >
              <Button type="dashed">大图标复制</Button>
            </CopyWrapper>
          </div>
        </div>
      </Card>
    </div>
  </Page>
</template>

<script lang="ts" setup>
import { Page } from '@vben/common-ui';
import { Button, Card, Tag } from 'ant-design-vue';
import { CopyWrapper } from '#/components/form';

defineOptions({
  name: 'CopyWrapperTest',
});
</script>


