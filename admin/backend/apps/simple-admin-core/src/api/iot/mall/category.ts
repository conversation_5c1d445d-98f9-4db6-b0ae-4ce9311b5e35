import { requestClient } from '#/api/request';
import { type BaseDataResp } from '../../model/baseModel';
import {
  type CategoryListReq,
  type CategoryListResp,
  type CategoryTreeReq,
  type CategoryTreeData,
  type CategoryCreateReq,
  type CategoryCreateResp,
  type CategoryUpdateReq,
  type CategoryUpdateResp,
  type CategoryDetailReq,
  type CategoryDetailResp,
  type CategoryDeleteReq,
  type CategoryDeleteResp,
  type CategoryMoveReq,
  type CategoryMoveResp,
} from '../model/categoryModel';

enum Api {
  GetCategoryTree = '/iot-api/mall/category/tree',
  GetCategoryList = '/iot-api/mall/category/list',
  CreateCategory = '/iot-api/mall/category/create',
  UpdateCategory = '/iot-api/mall/category/update',
  GetCategoryDetail = '/iot-api/mall/category/detail',
  DeleteCategory = '/iot-api/mall/category/delete',
  MoveCategory = '/iot-api/mall/category/move',
}

/**
 * @description: 获取分类树
 */
export const getCategoryTree = (params?: CategoryTreeReq) => {
  return requestClient.get<BaseDataResp<CategoryTreeData>>(Api.GetCategoryTree, {
    params,
  });
};

/**
 * @description: 获取分类列表
 */
export const getCategoryList = (params: CategoryListReq) => {
  return requestClient.post<BaseDataResp<CategoryListResp>>(Api.GetCategoryList, params);
};

/**
 * @description: 创建分类
 */
export const createCategory = (params: CategoryCreateReq) => {
  return requestClient.post<CategoryCreateResp>(Api.CreateCategory, params);
};

/**
 * @description: 更新分类
 */
export const updateCategory = (params: CategoryUpdateReq) => {
  return requestClient.put<CategoryUpdateResp>(Api.UpdateCategory, params);
};

/**
 * @description: 获取分类详情
 */
export const getCategoryDetail = (params: CategoryDetailReq) => {
  return requestClient.get<BaseDataResp<CategoryDetailResp>>(`${Api.GetCategoryDetail}/${params.id}`, {
    params: { includeChildren: params.includeChildren },
  });
};

/**
 * @description: 删除分类
 */
export const deleteCategory = (params: CategoryDeleteReq) => {
  return requestClient.delete<CategoryDeleteResp>(Api.DeleteCategory, { data: params });
};

/**
 * @description: 移动分类
 */
export const moveCategory = (params: CategoryMoveReq) => {
  return requestClient.put<CategoryMoveResp>(Api.MoveCategory, params);
};

/**
 * @description: 批量删除分类
 */
export const batchDeleteCategory = (ids: number[], force = false) => {
  return deleteCategory({ ids, force });
};

/**
 * @description: 获取顶级分类列表（用于下拉选择）
 */
export const getTopLevelCategories = () => {
  return getCategoryList({
    page: { page: 1, pageSize: 1000 },
    parentId: 0,
    status: 1, // 只获取启用状态的分类
  });
};

/**
 * @description: 获取指定分类的子分类列表
 */
export const getChildCategories = (parentId: number) => {
  return getCategoryList({
    page: { page: 1, pageSize: 1000 },
    parentId,
    status: 1,
  });
};
